# AI 智能体文本转 SQL 系统 - 详细架构说明

## 🎯 系统概述

本系统是基于 AutoGen 0.6 框架构建的多智能体协作系统，能够将用户的自然语言查询智能转换为 MySQL SQL 语句并安全执行。系统采用分层架构设计，通过多个专业智能体的协作完成复杂的文本到 SQL 转换任务。

## 🏗️ 整体架构

### 系统分层结构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层 (Frontend)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Web界面       │  │   命令行界面     │  │   API接口       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   智能体协作层 (Agents)                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  协调者智能体    │  │  SQL专家智能体   │  │  安全审查智能体  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐                     │
│  │  执行器智能体    │  │  格式化智能体    │                     │
│  └─────────────────┘  └─────────────────┘                     │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   数据处理层 (Database)                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   连接池管理     │  │   安全验证器     │  │   查询执行器     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   配置支撑层 (Config)                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   环境配置       │  │   日志系统       │  │   错误处理       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🤖 智能体详细设计

### 1. 协调者智能体 (CoordinatorAgent)

**职责**: 管理整个查询处理工作流程，协调各个智能体的协作

**核心功能**:

- 接收用户的自然语言查询
- 按顺序调用各个专业智能体
- 管理消息传递和状态跟踪
- 处理错误和异常情况
- 生成最终的处理结果

**工作流程**:

```python
workflow_steps = [
    ("sql_generation", sql_expert),      # SQL生成
    ("security_validation", security_agent),  # 安全验证
    ("sql_execution", executor_agent),   # SQL执行
    ("result_formatting", formatter_agent)    # 结果格式化
]
```

### 2. SQL 专家智能体 (SQLExpertAgent)

**职责**: 将自然语言查询转换为准确的 SQL 语句

**核心机制**:

#### 2.1 数据库模式获取

```python
async def _get_database_schema(self) -> Dict[str, Any]:
    # 1. 获取所有表名
    tables = await db_manager.get_all_tables()

    # 2. 获取每个表的结构信息
    for table in tables:
        if table not in self.schema_cache:
            self.schema_cache[table] = await db_manager.get_table_schema(table)
```

**表结构信息包括**:

- 列名 (COLUMN_NAME)
- 数据类型 (DATA_TYPE)
- 是否可空 (IS_NULLABLE)
- 默认值 (COLUMN_DEFAULT)
- 列注释 (COLUMN_COMMENT)

#### 2.2 智能提示词构建

系统会将用户查询和数据库模式信息组合成完整的提示词：

```python
def _build_query_prompt(self, query: str, schema_info: Dict[str, Any], metadata: Dict[str, Any]) -> str:
    schema_text = self._format_schema_info(schema_info)

    prompt = f"""
    用户查询：{query}

    数据库模式信息：
    {schema_text}

    请根据上述数据库模式，将用户的自然语言查询转换为准确的MySQL SQL语句。
    """
```

#### 2.3 AutoGen 模型集成

使用自定义的模型客户端支持本地模型：

```python
# 支持自定义模型配置
openai_client = openai.OpenAI(
    api_key=settings.openai.api_key,
    base_url=settings.openai.base_url,  # 支持本地模型
)
```

#### 2.4 响应解析

智能体返回结构化的 JSON 响应：

```json
{
  "sql": "生成的SQL语句",
  "explanation": "SQL语句的解释",
  "parameters": ["参数列表"],
  "estimated_rows": "预估返回行数",
  "confidence": "置信度(0-1)"
}
```

### 3. 安全审查智能体 (SecurityAgent)

**职责**: 对生成的 SQL 语句进行全面的安全检查

**安全检查机制**:

#### 3.1 SQL 操作类型验证

```python
def validate_sql_operation(self, sql: str) -> Tuple[bool, str]:
    parsed = sqlparse.parse(sql)[0]
    statement_type = self._get_statement_type(parsed)

    if statement_type not in settings.security.get_allowed_operations_list():
        return False, f"不允许的SQL操作类型: {statement_type}"
```

#### 3.2 SQL 注入检测

使用多种模式检测潜在的 SQL 注入攻击：

```python
INJECTION_PATTERNS = [
    r"(\bUNION\b.*\bSELECT\b)",
    r"(\bOR\b.*=.*)",
    r"(\bAND\b.*=.*)",
    r"(--.*)",
    r"(/\*.*\*/)",
    # ... 更多模式
]
```

#### 3.3 查询复杂度检查

- 嵌套层级限制
- JOIN 操作数量限制
- 子查询数量限制

### 4. 数据库执行智能体 (ExecutorAgent)

**职责**: 安全执行经过验证的 SQL 语句

**执行机制**:

#### 4.1 连接池管理

```python
@asynccontextmanager
async def get_connection(self):
    connection = None
    try:
        connection = self.pool.get_connection()
        if connection.is_connected():
            yield connection
    finally:
        if connection and connection.is_connected():
            connection.close()
```

#### 4.2 查询执行

- 设置查询超时限制
- 自动添加 LIMIT 子句防止大量数据返回
- 支持参数化查询防止注入
- 异常处理和回滚机制

#### 4.3 结果处理

```python
async def _execute_select_query(self, sql: str) -> Dict[str, Any]:
    # 添加行数限制
    if "LIMIT" not in sql.upper():
        sql = f"{sql.rstrip(';')} LIMIT {self.max_result_rows}"

    # 执行查询并转换为DataFrame
    df = await db_manager.execute_query_to_dataframe(sql)

    # 处理数据类型确保JSON序列化
    df_serializable = self._make_dataframe_serializable(df)
```

### 5. 结果格式化智能体 (FormatterAgent)

**职责**: 将查询结果格式化为用户友好的展示格式

**格式化功能**:

- 表格格式 (table)
- 富文本表格 (rich_table)
- JSON 格式 (json)
- CSV 格式 (csv)
- 摘要信息 (summary)

## 🔄 完整执行流程

### 用户查询处理步骤

1. **查询接收**

   - 用户输入自然语言查询
   - 系统创建 AgentMessage 对象
   - 协调者智能体接收消息

2. **SQL 生成阶段**

   ```
   用户查询 → SQL专家智能体
   ├── 获取数据库模式信息
   ├── 构建智能提示词
   ├── 调用AI模型生成SQL
   └── 解析并返回结构化结果
   ```

3. **安全验证阶段**

   ```
   生成的SQL → 安全审查智能体
   ├── SQL操作类型检查
   ├── SQL注入模式检测
   ├── 查询复杂度验证
   └── 返回安全检查结果
   ```

4. **SQL 执行阶段**

   ```
   验证通过的SQL → 数据库执行智能体
   ├── 获取数据库连接
   ├── 设置执行参数和超时
   ├── 执行SQL查询
   └── 处理和返回结果数据
   ```

5. **结果格式化阶段**
   ```
   查询结果 → 结果格式化智能体
   ├── 生成多种展示格式
   ├── 创建数据摘要
   ├── 美化输出格式
   └── 返回最终结果
   ```

## 🔍 表结构发现机制

### 智能表选择算法

系统通过以下机制确定查询涉及的表：

1. **关键词匹配**

   - 分析用户查询中的业务术语
   - 匹配表名和列名
   - 识别业务实体关系

2. **语义理解**

   - 利用 AI 模型理解查询意图
   - 根据数据库模式推断相关表
   - 考虑表之间的关联关系

3. **模式信息利用**

   ```python
   # 系统提供完整的数据库模式信息给AI模型
   schema_text = f"""
   数据库: {schema_info['database']}

   表: users
     - id: int NOT NULL -- 用户ID
     - name: varchar(100) NOT NULL -- 用户姓名
     - email: varchar(255) -- 邮箱地址

   表: orders
     - id: int NOT NULL -- 订单ID
     - user_id: int NOT NULL -- 用户ID
     - amount: decimal(10,2) -- 订单金额
   """
   ```

4. **智能推理**
   - AI 模型根据查询内容和表结构
   - 自动推断需要查询的表
   - 生成合适的 JOIN 条件

### 示例：查询处理过程

**用户输入**: "查询所有用户的订单总金额"

**处理过程**:

1. **意图识别**: 需要用户信息和订单信息
2. **表选择**: users 表和 orders 表
3. **关系推断**: 通过 user_id 关联
4. **SQL 生成**:
   ```sql
   SELECT u.name, SUM(o.amount) as total_amount
   FROM users u
   JOIN orders o ON u.id = o.user_id
   GROUP BY u.id, u.name
   ```

## 📊 性能优化机制

### 1. 缓存策略

- 数据库模式信息缓存
- 查询结果缓存（可选）
- 连接池复用

### 2. 查询优化

- 自动添加 LIMIT 限制
- 索引使用建议
- 查询计划分析

### 3. 并发控制

- 异步处理机制
- 连接池管理
- 资源限制控制

## 🛡️ 安全保障机制

### 多层安全防护

1. **输入验证层**

   - 查询长度限制
   - 特殊字符过滤
   - 编码验证

2. **SQL 解析层**

   - 语法解析验证
   - 操作类型检查
   - 结构完整性验证

3. **模式匹配层**

   - 注入模式检测
   - 危险关键词识别
   - 异常模式告警

4. **执行控制层**
   - 超时控制
   - 资源限制
   - 权限检查

## 🔧 配置和扩展

### 配置管理

系统支持灵活的配置管理：

```python
class DatabaseSettings(BaseSettings):
    host: str = Field(default="localhost", alias="DB_HOST")
    port: int = Field(default=3306, alias="DB_PORT")
    # ... 更多配置项
```

### 智能体扩展

系统采用插件化设计，可以轻松添加新的智能体：

```python
class CustomAgent(BaseAgent):
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        # 自定义处理逻辑
        pass
```

## 📈 监控和日志

### 完整的监控体系

- 查询处理时间统计
- 成功率和错误率监控
- 智能体性能分析
- 数据库连接状态监控

### 结构化日志

- 请求链路追踪
- 错误详情记录
- 性能指标收集
- 安全事件审计

## 💡 核心技术实现细节

### 消息传递机制

系统使用统一的消息格式在智能体间传递信息：

```python
@dataclass
class AgentMessage:
    type: MessageType          # 消息类型：QUERY, SQL, RESULT, ERROR等
    content: Any              # 消息内容
    metadata: Optional[Dict]  # 元数据信息
    sender: Optional[str]     # 发送者标识
    timestamp: Optional[str]  # 时间戳
```

**消息流转过程**:

```
用户查询 → QUERY消息 → SQL专家智能体
         ↓
SQL结果 → SQL消息 → 安全审查智能体
         ↓
验证结果 → VALIDATION消息 → 数据库执行智能体
         ↓
执行结果 → RESULT消息 → 结果格式化智能体
         ↓
格式化结果 → FORMAT消息 → 用户
```

### AutoGen 0.6 集成机制

#### 自定义模型客户端

系统实现了自定义的 ChatCompletionClient 来支持本地模型：

```python
class CustomChatCompletionClient(ChatCompletionClient):
    def __init__(self, openai_client, model_info):
        self._openai_client = openai_client
        self._model_info = model_info

    async def create(self, messages, **kwargs):
        # 转换消息格式
        openai_messages = []
        for msg in messages:
            if hasattr(msg, 'content'):
                role = msg.__class__.__name__.lower().replace('message', '')
                openai_messages.append({
                    "role": role,
                    "content": msg.content
                })

        # 调用模型API
        response = self._openai_client.chat.completions.create(
            model=self._model_info.model,
            messages=openai_messages,
            **kwargs
        )

        return CreateResult(
            content=response.choices[0].message.content,
            finish_reason=response.choices[0].finish_reason,
            usage=response.usage,
            cached=False
        )
```

#### 智能体初始化

每个智能体都基于 AutoGen 的 AssistantAgent：

```python
self.assistant = AssistantAgent(
    name="sql_expert",
    model_client=self._create_model_client(),
    system_message=self._get_sql_expert_prompt(),
)
```

### 数据库模式智能分析

#### 表结构缓存机制

```python
# 智能缓存策略
self.schema_cache: Dict[str, List[Dict[str, Any]]] = {}

async def _get_database_schema(self) -> Dict[str, Any]:
    tables = await db_manager.get_all_tables()
    schema_info = {"database": settings.database.database, "tables": {}}

    for table in tables:
        if table not in self.schema_cache:
            # 只有未缓存的表才重新获取结构
            self.schema_cache[table] = await db_manager.get_table_schema(table)
        schema_info["tables"][table] = self.schema_cache[table]
```

#### 智能表关联推断

系统通过以下方式推断表之间的关系：

1. **外键分析**: 通过 INFORMATION_SCHEMA 分析外键关系
2. **命名约定**: 识别类似 user_id, order_id 的关联字段
3. **语义分析**: AI 模型理解业务逻辑中的表关系

### SQL 生成的智能优化

#### 提示词工程

系统使用精心设计的提示词模板：

```python
def _get_sql_expert_prompt(self) -> str:
    return """
    你是一个专业的SQL专家，擅长将自然语言查询转换为准确的MySQL SQL语句。

    核心职责：
    1. 理解用户的自然语言查询意图
    2. 根据数据库模式生成准确的SQL语句
    3. 确保SQL语句的语法正确性和逻辑合理性
    4. 优化查询性能
    5. 只生成SELECT、INSERT、UPDATE、DELETE类型的语句

    重要规则：
    - 严格按照提供的数据库模式生成SQL
    - 使用参数化查询防止SQL注入
    - 对于模糊查询使用LIKE操作符
    - 合理使用索引和JOIN优化性能
    - 返回结果必须是有效的JSON格式
    """
```

#### 上下文增强

系统会在提示词中包含：

- 完整的数据库模式信息
- 表之间的关系说明
- 列的业务含义注释
- 查询优化建议

### 安全验证的多重机制

#### 1. 词法分析安全检查

```python
DANGEROUS_KEYWORDS = [
    "DROP", "DELETE", "TRUNCATE", "ALTER", "CREATE",
    "EXEC", "EXECUTE", "UNION", "SCRIPT", "DECLARE"
]

INJECTION_PATTERNS = [
    r"(\bUNION\b.*\bSELECT\b)",      # UNION注入
    r"(\bOR\b.*=.*)",                # OR条件注入
    r"(\bAND\b.*=.*)",               # AND条件注入
    r"(--.*)",                       # 注释注入
    r"(/\*.*\*/)",                   # 块注释注入
    r"(\bEXEC\b|\bEXECUTE\b)",       # 执行命令注入
    r"(\bINTO\b.*\bOUTFILE\b)",      # 文件写入注入
]
```

#### 2. 语法解析验证

使用 sqlparse 库进行深度语法分析：

```python
def validate_sql_operation(self, sql: str) -> Tuple[bool, str]:
    try:
        parsed = sqlparse.parse(sql)[0]
        statement_type = self._get_statement_type(parsed)

        if statement_type not in settings.security.get_allowed_operations_list():
            return False, f"不允许的SQL操作类型: {statement_type}"

        return True, ""
    except Exception as e:
        return False, f"SQL解析失败: {str(e)}"
```

#### 3. 复杂度控制

```python
def validate_query_complexity(self, sql: str) -> Tuple[bool, str]:
    # 检查嵌套层级
    nesting_level = sql.count("(") - sql.count(")")
    if abs(nesting_level) > 5:
        return False, "查询嵌套层级过深"

    # 检查JOIN数量
    join_count = len(re.findall(r"\bJOIN\b", sql, re.IGNORECASE))
    if join_count > 10:
        return False, "JOIN操作过多"

    # 检查子查询数量
    subquery_count = len(re.findall(r"\bSELECT\b", sql, re.IGNORECASE)) - 1
    if subquery_count > 5:
        return False, "子查询过多"
```

### 数据库执行的安全机制

#### 连接池管理

```python
class DatabaseManager:
    def _initialize_pool(self):
        pool_config = {
            'pool_name': 'mysql_pool',
            'pool_size': settings.database.pool_size,
            'pool_reset_session': True,
            'host': settings.database.host,
            'port': settings.database.port,
            'user': settings.database.user,
            'password': settings.database.password,
            'database': settings.database.database,
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_unicode_ci',
            'autocommit': True,
            'time_zone': '+00:00'
        }
        self.pool = pooling.MySQLConnectionPool(**pool_config)
```

#### 查询执行保护

```python
async def execute_query(self, query: str, params: Optional[Tuple] = None, fetch_results: bool = True):
    async with self.get_connection() as connection:
        cursor = connection.cursor(dictionary=True)
        try:
            # 设置查询超时
            cursor.execute(f"SET SESSION max_execution_time = {settings.security.max_query_timeout * 1000}")

            # 执行查询（使用参数化查询）
            cursor.execute(query, params or ())

            if fetch_results:
                results = cursor.fetchall()
                return results
            else:
                connection.commit()
                return None
        except Error as e:
            connection.rollback()
            raise
        finally:
            cursor.close()
```

### 结果格式化的多样性

#### 多格式支持

```python
def _format_select_result(self, result_data: Dict[str, Any]) -> Dict[str, Any]:
    data = result_data.get("data", [])
    columns = result_data.get("columns", [])

    # 生成多种格式
    formats = {
        "table": self._create_table_format(data, columns),
        "rich_table": self._create_rich_table_format(data, columns),
        "json": json.dumps(data, ensure_ascii=False, indent=2),
        "csv": self._create_csv_format(data, columns),
        "summary": self._create_summary_format(data, columns)
    }

    return {
        "formatted_output": self._create_main_output(data, columns, len(data), result_data.get("execution_time_ms", 0)),
        "summary": {
            "row_count": len(data),
            "column_count": len(columns),
            "execution_time_ms": result_data.get("execution_time_ms", 0),
            "message": f"成功返回 {len(data)} 行数据"
        },
        "formats": formats,
        "data_preview": data[:5] if len(data) > 5 else data
    }
```

## 🚀 系统启动和初始化流程

### 启动序列

```python
async def initialize(self) -> bool:
    try:
        logger.info("正在初始化AI智能体文本转SQL系统...")

        # 1. 测试数据库连接
        db_connected = await db_manager.test_connection()
        if not db_connected:
            logger.error("数据库连接失败，请检查配置")
            return False

        # 2. 初始化智能体
        workflow_status = await self.coordinator.get_workflow_status()

        # 3. 预热缓存（可选）
        await self._warm_up_cache()

        self.is_initialized = True
        logger.info("系统初始化完成")
        return True
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        return False
```

### 缓存预热

```python
async def _warm_up_cache(self):
    """预热数据库模式缓存"""
    try:
        tables = await db_manager.get_all_tables()
        for table in tables:
            await db_manager.get_table_schema(table)
        logger.info(f"预热了 {len(tables)} 个表的模式信息")
    except Exception as e:
        logger.warning(f"缓存预热失败: {e}")
```

这个系统通过多智能体的协作，实现了从自然语言到 SQL 的智能转换，同时保证了安全性、性能和可扩展性。每个组件都经过精心设计，确保系统的稳定性和可维护性。
