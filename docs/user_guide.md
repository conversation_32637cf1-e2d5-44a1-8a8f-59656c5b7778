# AI智能体文本转SQL系统 - 用户使用指南

## 🎯 系统简介

这是一个基于AutoGen 0.6框架的多智能体协作系统，能够将您的自然语言查询智能转换为MySQL SQL语句并自动执行。

## 🚀 快速开始

### 1. 启动系统

#### 命令行模式
```bash
# 交互式模式
python3 main.py interactive

# 演示模式
python3 main.py demo
```

#### Web界面模式
```bash
# 启动Web服务
python3 app.py
```
然后在浏览器中访问 `http://localhost:5000`

### 2. 基本使用

#### 自然语言查询示例

**简单查询**:
- "查询所有用户信息"
- "显示最近10个订单"
- "统计每个用户的订单数量"

**复杂查询**:
- "查询2023年销售额超过1000元的用户及其订单详情"
- "按月份统计订单总金额，并按金额降序排列"
- "查询购买过商品A但没有购买过商品B的用户"

## 📊 查询处理流程

当您输入自然语言查询时，系统会按以下步骤处理：

```
用户输入 → SQL生成 → 安全验证 → 数据库执行 → 结果格式化 → 展示结果
```

### 详细步骤说明

1. **SQL生成阶段**
   - 分析您的查询意图
   - 获取数据库表结构信息
   - 生成对应的SQL语句

2. **安全验证阶段**
   - 检查SQL注入风险
   - 验证操作类型是否允许
   - 评估查询复杂度

3. **数据库执行阶段**
   - 安全执行SQL语句
   - 控制查询超时和结果数量
   - 处理执行异常

4. **结果格式化阶段**
   - 生成多种展示格式
   - 创建数据摘要
   - 美化输出结果

## 🔍 系统如何理解您的查询

### 表结构自动发现

系统会自动分析数据库中的所有表结构，包括：
- 表名和列名
- 数据类型
- 列注释和业务含义
- 表之间的关联关系

### 智能表选择

当您提出查询时，系统通过以下方式确定需要查询的表：

1. **关键词匹配**: 识别查询中的业务术语
2. **语义理解**: AI模型理解查询意图
3. **关系推断**: 自动推断表之间的关联

**示例**:
- 查询："查询所有用户的订单总金额"
- 系统分析：需要用户表(users)和订单表(orders)
- 关系推断：通过user_id字段关联
- 生成SQL：
  ```sql
  SELECT u.name, SUM(o.amount) as total_amount
  FROM users u
  JOIN orders o ON u.id = o.user_id
  GROUP BY u.id, u.name
  ```

## 🛡️ 安全保障机制

### 多层安全防护

1. **输入验证**: 检查查询长度和特殊字符
2. **SQL注入防护**: 多种模式检测潜在攻击
3. **操作限制**: 只允许安全的查询操作
4. **复杂度控制**: 限制查询的复杂程度

### 安全特性

- ✅ 自动参数化查询
- ✅ SQL注入检测
- ✅ 危险操作拦截
- ✅ 查询超时控制
- ✅ 结果数量限制

## 📋 支持的查询类型

### ✅ 支持的操作

- **SELECT查询**: 数据查询和统计
- **INSERT操作**: 数据插入（需要明确指定）
- **UPDATE操作**: 数据更新（需要明确指定）
- **DELETE操作**: 数据删除（需要明确指定）

### ❌ 不支持的操作

- DROP、CREATE等DDL操作
- 存储过程和函数调用
- 系统表访问
- 文件操作相关SQL

## 🎨 结果展示格式

系统提供多种结果展示格式：

### 1. 表格格式
```
┌─────────┬──────────────┬─────────────┐
│ 用户ID  │ 用户名       │ 订单总金额   │
├─────────┼──────────────┼─────────────┤
│ 1       │ 张三         │ 1500.00     │
│ 2       │ 李四         │ 2300.50     │
└─────────┴──────────────┴─────────────┘
```

### 2. JSON格式
```json
[
  {"用户ID": 1, "用户名": "张三", "订单总金额": 1500.00},
  {"用户ID": 2, "用户名": "李四", "订单总金额": 2300.50}
]
```

### 3. CSV格式
```csv
用户ID,用户名,订单总金额
1,张三,1500.00
2,李四,2300.50
```

### 4. 摘要信息
- 查询执行时间
- 返回记录数量
- 涉及的表和列
- 性能统计信息

## 💡 使用技巧

### 1. 提高查询准确性

**明确查询意图**:
- ❌ "查询数据"
- ✅ "查询所有用户的基本信息"

**指定时间范围**:
- ❌ "查询订单"
- ✅ "查询2023年的所有订单"

**明确统计维度**:
- ❌ "统计销售额"
- ✅ "按月份统计销售额"

### 2. 优化查询性能

**限制结果数量**:
- "查询最近100个订单"
- "显示前10名销售额最高的用户"

**使用索引字段**:
- 尽量在查询条件中使用主键或索引字段
- 避免在大表上进行全表扫描

### 3. 处理复杂查询

**分步查询**:
对于复杂的业务需求，可以分解为多个简单查询

**使用具体的业务术语**:
系统能更好地理解具体的业务概念

## 🔧 常见问题解决

### Q1: 查询结果为空
**可能原因**:
- 查询条件过于严格
- 表中确实没有符合条件的数据
- 表名或字段名理解错误

**解决方法**:
- 放宽查询条件
- 先查询表的基本信息
- 使用更准确的业务术语

### Q2: 查询执行失败
**可能原因**:
- SQL语法错误
- 权限不足
- 查询超时

**解决方法**:
- 简化查询条件
- 检查数据库连接
- 联系管理员检查权限

### Q3: 结果不符合预期
**可能原因**:
- 查询意图理解偏差
- 表关联关系错误
- 数据本身的问题

**解决方法**:
- 重新描述查询需求
- 检查生成的SQL语句
- 验证数据的正确性

## 📞 获取帮助

### 系统状态检查
```bash
# 检查系统状态
python3 main.py status

# 查看日志
tail -f logs/system.log
```

### 调试模式
```bash
# 启用详细日志
export LOG_LEVEL=DEBUG
python3 main.py interactive
```

### 联系支持
- 查看系统日志获取详细错误信息
- 提供具体的查询内容和错误信息
- 描述期望的查询结果

## 🎯 最佳实践

1. **明确表达查询意图**: 使用清晰、具体的自然语言描述
2. **逐步复杂化**: 从简单查询开始，逐步增加复杂度
3. **验证结果**: 检查返回的SQL语句和结果是否符合预期
4. **合理使用限制**: 对大表查询添加适当的限制条件
5. **关注性能**: 注意查询执行时间，优化复杂查询

通过遵循这些指南，您可以更有效地使用AI智能体文本转SQL系统，获得准确、安全的查询结果。
