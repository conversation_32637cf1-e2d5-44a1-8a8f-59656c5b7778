# AutoGen 框架在智能体系统中的使用分析

## 🤔 您的问题很好！让我详细解释 AutoGen 的使用方式

您提到的问题非常关键：**这个系统确实没有使用 AutoGen 的传统聊天模式或角色系统，而是采用了一种混合架构**。

## 🏗️ AutoGen 使用方式分析

### 1. AutoGen 的实际使用范围

**只有 SQL 专家智能体使用了 AutoGen 的 AssistantAgent**：

```python
# agents/sql_expert_agent.py
class SQLExpertAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="SQL专家",
            description="将自然语言查询转换为准确、安全的SQL语句"
        )

        # 🎯 这里是唯一使用AutoGen的地方
        self.assistant = AssistantAgent(
            name="sql_expert",
            model_client=self._create_model_client(),
            system_message=self._get_sql_expert_prompt(),
        )
```

**其他智能体都是自定义实现**：

- 安全审查智能体 (SecurityAgent)
- 数据库执行智能体 (ExecutorAgent)
- 结果格式化智能体 (FormatterAgent)
- 协调者智能体 (CoordinatorAgent)

### 2. 为什么这样设计？

#### 原因分析：

1. **只有 SQL 生成需要 AI 模型**

   - 其他智能体都是基于规则和逻辑的确定性处理
   - 只有自然语言转 SQL 需要大模型的推理能力

2. **性能考虑**

   - 避免每个步骤都调用 AI 模型
   - 减少 API 调用成本和延迟

3. **可控性要求**
   - 安全检查、数据库执行等需要精确控制
   - 不能依赖 AI 模型的不确定性

## 🔗 智能体衔接机制详解

### 1. 自定义消息传递系统

系统使用自定义的消息格式，而不是 AutoGen 的原生消息系统：

```python
# agents/base_agent.py
@dataclass
class AgentMessage:
    type: MessageType          # QUERY, SQL, VALIDATION, RESULT, FORMAT, ERROR
    content: Any              # 消息内容
    metadata: Optional[Dict[str, Any]] = None
    sender: Optional[str] = None
    timestamp: Optional[str] = None
```

### 2. 协调者智能体的工作流程编排

```python
# agents/coordinator_agent.py
class CoordinatorAgent(BaseAgent):
    def __init__(self):
        # 🎯 定义工作流程步骤
        self.workflow_steps = [
            ("sql_generation", sql_expert),        # SQL专家智能体
            ("security_validation", security_agent), # 安全审查智能体
            ("sql_execution", executor_agent),     # 数据库执行智能体
            ("result_formatting", formatter_agent)  # 结果格式化智能体
        ]

    async def _execute_workflow(self, query: str, session_id: str):
        # 创建初始消息
        current_message = AgentMessage(
            type=MessageType.QUERY,
            content=query,
            metadata={"session_id": session_id},
            sender="user"
        )

        # 🔄 逐步执行工作流程
        for step_name, agent in self.workflow_steps:
            logger.info(f"执行步骤: {step_name} - {agent.name}")

            # 调用智能体处理消息
            step_result = await agent.process_message(current_message)

            # 检查是否成功
            if step_result.type == MessageType.ERROR:
                # 处理错误，终止流程
                break

            # 🔄 将当前步骤的输出作为下一步的输入
            current_message = step_result
```

### 3. 消息类型转换机制

每个智能体都有特定的输入输出消息类型：

```python
# 消息流转示例
用户查询 → QUERY消息 → SQL专家智能体
         ↓
SQL结果 → SQL消息 → 安全审查智能体
         ↓
验证结果 → VALIDATION消息 → 数据库执行智能体
         ↓
执行结果 → RESULT消息 → 结果格式化智能体
         ↓
格式化结果 → FORMAT消息 → 用户
```

## 🧠 SQL 专家智能体中 AutoGen 的具体使用

### 1. 自定义模型客户端

```python
def _create_model_client(self):
    """创建模型客户端 - 支持自定义模型"""
    import openai
    from autogen_core.models import ChatCompletionClient, ModelInfo, ModelCapabilities

    # 🔧 创建自定义OpenAI客户端（支持本地模型）
    openai_client = openai.OpenAI(
        api_key=settings.openai.api_key,
        base_url=settings.openai.base_url,  # 本地模型地址
    )

    # 🎯 为自定义模型创建模型信息
    model_info = ModelInfo(
        model=settings.openai.model,  # deepseek-v3-0324
        model_capabilities=ModelCapabilities(
            vision=False,
            function_calling=True,
            json_output=True,
        ),
    )

    # 🔧 创建自定义ChatCompletionClient
    class CustomChatCompletionClient(ChatCompletionClient):
        # ... 实现细节

    return CustomChatCompletionClient(openai_client, model_info)
```

### 2. AutoGen 助手的调用方式

```python
async def process_message(self, message: AgentMessage) -> AgentMessage:
    """处理自然语言查询请求"""
    # 获取数据库模式信息
    schema_info = await self._get_database_schema()

    # 构建完整的提示词
    full_prompt = self._build_query_prompt(query, schema_info, metadata)

    # 🎯 调用AutoGen助手生成SQL
    response = await self.assistant.on_messages(
        [TextMessage(content=full_prompt, source="user")],
        cancellation_token=None,
    )

    # 解析响应
    sql_result = self._parse_sql_response(response.chat_message.content)

    return self.create_response(
        MessageType.SQL, sql_result, {"original_query": query}
    )
```

## 🔄 完整的智能体衔接流程代码

### 1. 主程序启动

```python
# main.py
class TextToSQLSystem:
    def __init__(self):
        self.coordinator = coordinator  # 协调者智能体实例

    async def process_query(self, query: str, session_id: str = "default"):
        """处理用户查询"""
        # 创建查询消息
        message = AgentMessage(
            type=MessageType.QUERY,
            content=query,
            metadata={"session_id": session_id},
            sender="user"
        )

        # 🎯 交给协调者智能体处理
        result = await self.coordinator.process_message(message)
        return result.content
```

### 2. 协调者智能体的详细实现

```python
# agents/coordinator_agent.py
async def _execute_workflow(self, query: str, session_id: str) -> Dict[str, Any]:
    """执行完整工作流程"""
    workflow_result = {
        "success": False,
        "steps": {},
        "total_time_ms": 0,
        "session_id": session_id
    }

    # 创建初始消息
    current_message = AgentMessage(
        type=MessageType.QUERY,
        content=query,
        metadata={"session_id": session_id},
        sender="user"
    )

    start_time = datetime.now()

    try:
        # 🔄 逐步执行工作流程
        for step_name, agent in self.workflow_steps:
            logger.info(f"执行步骤: {step_name} - {agent.name}")

            step_start_time = datetime.now()

            # 🎯 执行当前步骤
            step_result = await agent.process_message(current_message)

            step_end_time = datetime.now()
            step_duration = (step_end_time - step_start_time).total_seconds() * 1000

            # 记录步骤结果
            workflow_result["steps"][step_name] = {
                "agent": agent.name,
                "start_time": step_start_time.isoformat(),
                "end_time": step_end_time.isoformat(),
                "duration_ms": round(step_duration, 2),
                "success": step_result.type != MessageType.ERROR,
                "result": step_result.content,
                "message_type": step_result.type.value
            }

            # 检查步骤是否成功
            if step_result.type == MessageType.ERROR:
                workflow_result["error"] = step_result.content
                workflow_result["failed_step"] = step_name
                logger.error(f"工作流程在步骤 {step_name} 失败: {step_result.content}")
                break

            # 🔄 准备下一步的输入 - 关键的衔接机制
            current_message = step_result

        else:
            # 所有步骤都成功完成
            workflow_result["success"] = True
            workflow_result["final_result"] = current_message.content

    except Exception as e:
        workflow_result["error"] = str(e)
        logger.error(f"工作流程执行异常: {e}")

    finally:
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds() * 1000
        workflow_result["total_time_ms"] = round(total_duration, 2)

    return workflow_result
```

### 3. 各智能体的 process_message 实现

```python
# 每个智能体都实现相同的接口
class BaseAgent(ABC):
    @abstractmethod
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理接收到的消息"""
        pass

# SQL专家智能体 - 使用AutoGen
class SQLExpertAgent(BaseAgent):
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        # 输入: QUERY消息
        # 输出: SQL消息
        pass

# 安全审查智能体 - 纯逻辑实现
class SecurityAgent(BaseAgent):
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        # 输入: SQL消息
        # 输出: VALIDATION消息 或 ERROR消息
        pass

# 数据库执行智能体 - 纯逻辑实现
class ExecutorAgent(BaseAgent):
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        # 输入: VALIDATION消息
        # 输出: RESULT消息 或 ERROR消息
        pass

# 结果格式化智能体 - 纯逻辑实现
class FormatterAgent(BaseAgent):
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        # 输入: RESULT消息
        # 输出: FORMAT消息
        pass
```

## 🎯 总结：混合架构的优势

### 1. AutoGen 的使用策略

- **精准使用**: 只在需要 AI 推理的地方使用 AutoGen
- **成本控制**: 避免不必要的 AI 模型调用
- **性能优化**: 确定性任务用传统编程，不确定性任务用 AI

### 2. 智能体衔接机制

- **统一接口**: 所有智能体都实现`process_message`方法
- **消息传递**: 通过标准化的`AgentMessage`进行通信
- **流程编排**: 协调者智能体管理整个工作流程
- **错误处理**: 每个步骤都有错误检查和处理机制

### 3. 架构优势

- **模块化**: 每个智能体职责单一，易于维护
- **可扩展**: 可以轻松添加新的智能体
- **可控制**: 关键步骤（安全、执行）使用确定性逻辑
- **高效**: 只在必要时使用 AI 模型

## 💻 完整的代码示例：智能体衔接过程

### 1. 用户查询的完整处理流程

```python
# 用户输入
user_query = "查询所有用户的订单总金额"

# 1. 主程序调用
system = TextToSQLSystem()
result = await system.process_query(user_query)

# 2. 系统内部处理流程
async def process_query(self, query: str, session_id: str = "default"):
    # 创建初始消息
    message = AgentMessage(
        type=MessageType.QUERY,
        content=query,
        metadata={"session_id": session_id},
        sender="user"
    )

    # 交给协调者智能体
    result = await self.coordinator.process_message(message)
    return result.content
```

### 2. 协调者智能体的具体衔接代码

```python
# agents/coordinator_agent.py
class CoordinatorAgent(BaseAgent):
    def __init__(self):
        # 定义工作流程 - 这是衔接的核心
        self.workflow_steps = [
            ("sql_generation", sql_expert),        # 步骤1: SQL生成
            ("security_validation", security_agent), # 步骤2: 安全验证
            ("sql_execution", executor_agent),     # 步骤3: SQL执行
            ("result_formatting", formatter_agent)  # 步骤4: 结果格式化
        ]

    async def _execute_workflow(self, query: str, session_id: str):
        # 创建初始消息
        current_message = AgentMessage(
            type=MessageType.QUERY,
            content=query,
            metadata={"session_id": session_id},
            sender="user"
        )

        # 🔄 关键的衔接循环
        for step_name, agent in self.workflow_steps:
            print(f"🔄 执行步骤: {step_name} - {agent.name}")
            print(f"📥 输入消息类型: {current_message.type.value}")

            # 调用智能体处理
            step_result = await agent.process_message(current_message)

            print(f"📤 输出消息类型: {step_result.type.value}")

            # 错误检查
            if step_result.type == MessageType.ERROR:
                print(f"❌ 步骤失败: {step_result.content}")
                break

            # 🎯 关键：将输出作为下一步的输入
            current_message = step_result
            print(f"✅ 步骤完成，传递给下一个智能体\n")

        return current_message
```

### 3. 各智能体的具体实现对比

#### SQL 专家智能体 - 使用 AutoGen

```python
# agents/sql_expert_agent.py
class SQLExpertAgent(BaseAgent):
    def __init__(self):
        # 🔥 这里使用AutoGen的AssistantAgent
        self.assistant = AssistantAgent(
            name="sql_expert",
            model_client=self._create_model_client(),
            system_message=self._get_sql_expert_prompt(),
        )

    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """
        输入: QUERY消息 (用户的自然语言查询)
        输出: SQL消息 (生成的SQL语句和元数据)
        """
        print(f"🧠 SQL专家智能体处理: {message.content}")

        # 获取数据库模式
        schema_info = await self._get_database_schema()

        # 构建提示词
        full_prompt = self._build_query_prompt(
            message.content, schema_info, message.metadata
        )

        # 🤖 调用AutoGen助手 - 这是唯一使用AutoGen对话的地方
        response = await self.assistant.on_messages(
            [TextMessage(content=full_prompt, source="user")],
            cancellation_token=None,
        )

        # 解析AI响应
        sql_result = self._parse_sql_response(response.chat_message.content)

        print(f"🔧 生成的SQL: {sql_result.get('sql', 'N/A')}")

        # 返回SQL消息
        return self.create_response(
            MessageType.SQL,
            sql_result,
            {"original_query": message.content}
        )
```

#### 安全审查智能体 - 纯逻辑实现

```python
# agents/security_agent.py
class SecurityAgent(BaseAgent):
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """
        输入: SQL消息 (生成的SQL语句)
        输出: VALIDATION消息 (安全验证结果) 或 ERROR消息
        """
        print(f"🛡️ 安全审查智能体处理SQL验证")

        if message.type != MessageType.SQL:
            return self.create_response(
                MessageType.ERROR,
                {"error": "安全审查智能体只处理SQL类型的消息"}
            )

        sql_data = message.content
        sql_statement = sql_data.get("sql", "")

        print(f"🔍 检查SQL: {sql_statement}")

        # 🔒 执行多层安全检查 - 纯逻辑实现
        security_result = await self._comprehensive_security_check(sql_statement)

        if security_result["is_safe"]:
            print("✅ 安全检查通过")
            return self.create_response(
                MessageType.VALIDATION,
                {
                    "status": "approved",
                    "sql": sql_statement,
                    "original_data": sql_data,
                    "security_checks": security_result
                }
            )
        else:
            print(f"❌ 安全检查失败: {security_result['threats']}")
            return self.create_response(
                MessageType.ERROR,
                {
                    "status": "rejected",
                    "sql": sql_statement,
                    "security_issues": security_result["threats"]
                }
            )
```

#### 数据库执行智能体 - 纯逻辑实现

```python
# agents/executor_agent.py
class ExecutorAgent(BaseAgent):
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """
        输入: VALIDATION消息 (验证通过的SQL)
        输出: RESULT消息 (查询结果) 或 ERROR消息
        """
        print(f"💾 数据库执行智能体处理SQL执行")

        if message.type != MessageType.VALIDATION:
            return self.create_response(
                MessageType.ERROR,
                {"error": "执行器只处理验证类型的消息"}
            )

        validation_data = message.content
        sql_statement = validation_data.get("sql", "")

        print(f"🚀 执行SQL: {sql_statement}")

        # 💾 执行SQL - 纯逻辑实现
        execution_result = await self._execute_sql_safely(sql_statement)

        print(f"📊 查询结果: {execution_result.get('row_count', 0)} 行")

        return self.create_response(
            MessageType.RESULT,
            execution_result,
            {
                "original_sql": sql_statement,
                "security_checks": validation_data.get("security_checks", {})
            }
        )
```

#### 结果格式化智能体 - 纯逻辑实现

```python
# agents/formatter_agent.py
class FormatterAgent(BaseAgent):
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """
        输入: RESULT消息 (查询结果)
        输出: FORMAT消息 (格式化的结果)
        """
        print(f"🎨 结果格式化智能体处理结果格式化")

        if message.type != MessageType.RESULT:
            return self.create_response(
                MessageType.ERROR,
                {"error": "格式化器只处理结果类型的消息"}
            )

        result_data = message.content

        # 🎨 格式化结果 - 纯逻辑实现
        if result_data.get("success", False):
            formatted_result = self._format_select_result(result_data)
            print(f"✨ 格式化完成: {formatted_result['summary']['row_count']} 行数据")
        else:
            formatted_result = self._format_error_result(result_data)
            print(f"❌ 格式化错误结果")

        return self.create_response(
            MessageType.FORMAT,
            formatted_result,
            {"original_result": result_data}
        )
```

### 4. 消息传递的完整示例

```python
# 完整的消息传递流程示例
async def demo_message_flow():
    """演示完整的消息传递流程"""

    # 1. 用户查询
    user_query = "查询所有用户的订单总金额"
    print(f"👤 用户查询: {user_query}\n")

    # 2. 创建初始消息
    message1 = AgentMessage(
        type=MessageType.QUERY,
        content=user_query,
        sender="user"
    )
    print(f"📝 创建QUERY消息: {message1.type.value}\n")

    # 3. SQL专家智能体处理
    message2 = await sql_expert.process_message(message1)
    print(f"🧠 SQL专家输出: {message2.type.value}")
    print(f"   生成的SQL: {message2.content.get('sql', 'N/A')}\n")

    # 4. 安全审查智能体处理
    message3 = await security_agent.process_message(message2)
    print(f"🛡️ 安全审查输出: {message3.type.value}")
    if message3.type == MessageType.VALIDATION:
        print(f"   验证状态: {message3.content.get('status', 'N/A')}\n")
    else:
        print(f"   错误信息: {message3.content.get('error', 'N/A')}\n")
        return  # 安全检查失败，终止流程

    # 5. 数据库执行智能体处理
    message4 = await executor_agent.process_message(message3)
    print(f"💾 数据库执行输出: {message4.type.value}")
    print(f"   查询结果: {message4.content.get('row_count', 0)} 行\n")

    # 6. 结果格式化智能体处理
    message5 = await formatter_agent.process_message(message4)
    print(f"🎨 格式化输出: {message5.type.value}")
    print(f"   最终结果: 已格式化完成\n")

    return message5

# 运行演示
# asyncio.run(demo_message_flow())
```

## 🎯 关键总结

### AutoGen 的使用特点：

1. **单点使用**: 只在 SQL 专家智能体中使用 AutoGen
2. **非聊天模式**: 不使用 AutoGen 的对话或角色系统
3. **工具化使用**: 将 AutoGen 作为 AI 推理工具，而非对话框架

### 智能体衔接机制：

1. **统一接口**: 所有智能体实现`process_message`方法
2. **消息传递**: 通过`AgentMessage`进行标准化通信
3. **流程编排**: 协调者智能体管理工作流程
4. **类型转换**: 每个智能体处理特定的消息类型

这种设计既利用了 AutoGen 的 AI 能力，又保持了系统的可控性和性能，是一个很好的混合架构实践。
