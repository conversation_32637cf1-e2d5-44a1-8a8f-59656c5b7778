# Web界面使用指南

## 概述

AI智能体文本转SQL系统现在提供了现代化的Web界面，让您可以通过浏览器轻松使用系统的所有功能。

## 功能特性

### 🔍 智能查询
- **自然语言输入**: 支持中文自然语言查询
- **实时结果展示**: 查询结果实时显示，包括SQL语句和数据表格
- **查询示例**: 提供常用查询示例，点击即可使用
- **快捷键支持**: Ctrl+Enter 快速提交查询

### 📋 批量查询
- **多查询处理**: 一次性处理多个查询
- **批量结果展示**: 清晰显示每个查询的执行结果
- **进度跟踪**: 实时显示批量处理进度

### 📚 查询历史
- **历史记录**: 自动保存所有查询历史
- **结果回顾**: 点击历史记录可重新查看结果
- **状态标识**: 清晰标识查询成功或失败状态

### ⚙️ 系统状态
- **实时监控**: 实时显示系统和数据库连接状态
- **配置信息**: 查看当前系统配置
- **系统初始化**: 一键初始化系统

### 🔄 实时更新
- **WebSocket连接**: 实时接收系统状态更新
- **自动刷新**: 查询历史和状态信息自动更新
- **通知提醒**: 操作结果实时通知

## 启动方式

### 方法一：使用启动脚本（推荐）
```bash
python3 start_web_server.py
```

### 方法二：直接启动API服务器
```bash
python3 api_server.py
```

### 方法三：使用uvicorn命令
```bash
uvicorn api_server:app --host 0.0.0.0 --port 8000 --reload
```

## 访问地址

启动成功后，在浏览器中访问：
- **主界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **API交互**: http://localhost:8000/redoc

## 界面说明

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│                    系统标题 + 状态指示器                    │
├─────────────┬───────────────────────────────────────────┤
│             │                                           │
│   导航菜单   │              内容区域                      │
│             │                                           │
│ • 智能查询   │  ┌─────────────────────────────────────┐  │
│ • 批量查询   │  │                                     │  │
│ • 查询历史   │  │            功能面板                  │  │
│ • 系统状态   │  │                                     │  │
│             │  └─────────────────────────────────────┘  │
└─────────────┴───────────────────────────────────────────┘
```

### 智能查询页面
1. **查询输入框**: 输入自然语言查询
2. **执行按钮**: 提交查询请求
3. **查询示例**: 预设的常用查询模板
4. **结果展示区**: 显示查询结果和SQL语句

### 批量查询页面
1. **批量输入框**: 每行输入一个查询
2. **批量执行**: 一次性处理所有查询
3. **结果汇总**: 显示批量处理统计信息
4. **详细结果**: 展示每个查询的具体结果

### 查询历史页面
1. **历史列表**: 按时间倒序显示查询历史
2. **状态标识**: 成功/失败状态一目了然
3. **快速操作**: 点击历史记录重新执行
4. **清空功能**: 一键清空历史记录

### 系统状态页面
1. **系统信息**: 显示系统初始化状态
2. **数据库状态**: 显示数据库连接信息
3. **AI模型配置**: 显示当前使用的AI模型
4. **工作流程状态**: 显示智能体工作状态

## 使用流程

### 首次使用
1. 启动Web服务器
2. 在浏览器中打开 http://localhost:8000
3. 点击"系统状态"标签页
4. 点击"初始化系统"按钮
5. 等待系统初始化完成

### 日常使用
1. 在"智能查询"页面输入自然语言查询
2. 点击"执行查询"或按 Ctrl+Enter
3. 查看查询结果和生成的SQL语句
4. 在"查询历史"中回顾之前的查询

### 批量处理
1. 切换到"批量查询"页面
2. 在输入框中每行输入一个查询
3. 点击"批量执行"
4. 查看批量处理结果

## API接口

Web界面基于RESTful API构建，主要接口包括：

### 查询接口
- `POST /api/query` - 处理单个查询
- `POST /api/batch-query` - 处理批量查询
- `GET /api/history` - 获取查询历史

### 系统接口
- `POST /api/initialize` - 初始化系统
- `GET /api/status` - 获取系统状态

### WebSocket接口
- `WS /ws` - 实时状态更新

## 技术架构

### 后端技术栈
- **FastAPI**: 现代化的Python Web框架
- **WebSocket**: 实时双向通信
- **Uvicorn**: 高性能ASGI服务器
- **Pydantic**: 数据验证和序列化

### 前端技术栈
- **原生JavaScript**: 无框架依赖，轻量高效
- **CSS3**: 现代化样式设计
- **WebSocket API**: 实时状态更新
- **Fetch API**: 异步HTTP请求

### 设计特点
- **响应式设计**: 支持桌面和移动设备
- **实时更新**: WebSocket实现实时状态同步
- **用户友好**: 直观的界面设计和操作流程
- **错误处理**: 完善的错误提示和异常处理

## 故障排除

### 常见问题

1. **无法访问Web界面**
   - 检查服务器是否正常启动
   - 确认端口8000未被占用
   - 检查防火墙设置

2. **系统初始化失败**
   - 检查数据库连接配置
   - 确认AI模型配置正确
   - 查看日志文件获取详细错误信息

3. **查询执行失败**
   - 确认系统已正确初始化
   - 检查数据库连接状态
   - 验证查询语句的合法性

4. **WebSocket连接失败**
   - 检查浏览器是否支持WebSocket
   - 确认网络连接正常
   - 尝试刷新页面重新连接

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看Web服务器日志
# 日志会直接输出到控制台
```

## 安全注意事项

1. **网络安全**
   - 生产环境建议使用HTTPS
   - 配置适当的防火墙规则
   - 限制访问IP范围

2. **数据安全**
   - 定期备份查询历史
   - 敏感查询及时清理
   - 监控异常查询行为

3. **系统安全**
   - 保持系统和依赖包更新
   - 定期检查安全漏洞
   - 配置适当的访问权限

## 性能优化

1. **查询优化**
   - 避免过于复杂的查询
   - 合理使用批量查询功能
   - 定期清理查询历史

2. **系统优化**
   - 监控系统资源使用
   - 适当调整并发连接数
   - 优化数据库连接池配置

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持智能查询和批量查询
- 实现查询历史和系统状态监控
- 提供WebSocket实时更新功能
