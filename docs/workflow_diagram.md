# AI智能体文本转SQL系统 - 工作流程图

## 🔄 系统整体工作流程

### 主要处理流程

```mermaid
graph TD
    A[用户输入自然语言查询] --> B[协调者智能体接收]
    B --> C[SQL专家智能体]
    C --> D[获取数据库模式]
    D --> E[构建智能提示词]
    E --> F[调用AI模型生成SQL]
    F --> G[解析SQL响应]
    G --> H[安全审查智能体]
    H --> I[SQL注入检测]
    I --> J[操作类型验证]
    J --> K[查询复杂度检查]
    K --> L{安全检查通过?}
    L -->|是| M[数据库执行智能体]
    L -->|否| N[返回安全错误]
    M --> O[获取数据库连接]
    O --> P[执行SQL查询]
    P --> Q[处理查询结果]
    Q --> R[结果格式化智能体]
    R --> S[生成多种展示格式]
    S --> T[创建结果摘要]
    T --> U[返回格式化结果给用户]
    N --> U
```

## 🧠 SQL专家智能体详细流程

### 自然语言到SQL转换过程

```mermaid
graph TD
    A[接收自然语言查询] --> B[检查消息类型]
    B --> C{是否为QUERY类型?}
    C -->|否| D[返回错误消息]
    C -->|是| E[获取数据库模式信息]
    E --> F[检查模式缓存]
    F --> G{缓存是否存在?}
    G -->|是| H[使用缓存数据]
    G -->|否| I[查询数据库获取表结构]
    I --> J[更新模式缓存]
    J --> H
    H --> K[格式化模式信息]
    K --> L[构建完整提示词]
    L --> M[调用AutoGen助手]
    M --> N[AI模型处理]
    N --> O[接收模型响应]
    O --> P[解析响应内容]
    P --> Q{响应格式是否正确?}
    Q -->|是| R[提取SQL和元数据]
    Q -->|否| S[尝试提取SQL语句]
    S --> T{提取成功?}
    T -->|是| R
    T -->|否| U[返回解析错误]
    R --> V[创建SQL消息响应]
    V --> W[返回给协调者]
    D --> W
    U --> W
```

## 🔒 安全审查智能体流程

### 多层安全检查机制

```mermaid
graph TD
    A[接收SQL消息] --> B[提取SQL语句]
    B --> C[SQL操作类型验证]
    C --> D{操作类型是否允许?}
    D -->|否| E[记录安全威胁]
    D -->|是| F[SQL注入检测]
    F --> G[检查危险关键词]
    G --> H[检查注入模式]
    H --> I[语法解析验证]
    I --> J{发现注入威胁?}
    J -->|是| E
    J -->|否| K[查询复杂度检查]
    K --> L[检查嵌套层级]
    L --> M[检查JOIN数量]
    M --> N[检查子查询数量]
    N --> O{复杂度超限?}
    O -->|是| E
    O -->|否| P[敏感操作检查]
    P --> Q[生成安全建议]
    Q --> R{所有检查通过?}
    R -->|是| S[创建VALIDATION消息]
    R -->|否| T[创建ERROR消息]
    E --> T
    S --> U[返回给协调者]
    T --> U
```

## 💾 数据库执行智能体流程

### 安全执行SQL语句

```mermaid
graph TD
    A[接收VALIDATION消息] --> B[检查验证状态]
    B --> C{验证是否通过?}
    C -->|否| D[返回验证错误]
    C -->|是| E[提取SQL语句]
    E --> F[判断SQL类型]
    F --> G{是SELECT查询?}
    G -->|是| H[执行SELECT查询]
    G -->|否| I[执行修改查询]
    H --> J[检查LIMIT子句]
    J --> K{是否有LIMIT?}
    K -->|否| L[自动添加LIMIT]
    K -->|是| M[获取数据库连接]
    L --> M
    M --> N[设置查询超时]
    N --> O[执行SQL查询]
    O --> P[获取查询结果]
    P --> Q[转换为DataFrame]
    Q --> R[处理数据类型]
    R --> S[计算执行时间]
    S --> T[创建RESULT消息]
    I --> U[执行修改操作]
    U --> V[获取影响行数]
    V --> W[提交事务]
    W --> S
    T --> X[返回给协调者]
    D --> X
```

## 🎨 结果格式化智能体流程

### 多格式结果展示

```mermaid
graph TD
    A[接收RESULT消息] --> B[检查执行状态]
    B --> C{执行是否成功?}
    C -->|否| D[格式化错误结果]
    C -->|是| E[获取结果数据]
    E --> F[判断SQL类型]
    F --> G{是SELECT查询?}
    G -->|是| H[格式化SELECT结果]
    G -->|否| I[格式化修改结果]
    H --> J[提取数据和列信息]
    J --> K[生成表格格式]
    K --> L[生成富文本表格]
    L --> M[生成JSON格式]
    M --> N[生成CSV格式]
    N --> O[生成数据摘要]
    O --> P[创建主要输出]
    P --> Q[生成数据预览]
    Q --> R[创建FORMAT消息]
    I --> S[格式化修改统计]
    S --> T[生成操作摘要]
    T --> R
    D --> U[创建错误格式消息]
    U --> V[返回给协调者]
    R --> V
```

## 🔄 协调者智能体工作流程

### 整体流程协调

```mermaid
graph TD
    A[接收用户查询] --> B[创建会话ID]
    B --> C[初始化工作流程结果]
    C --> D[开始执行工作流程]
    D --> E[SQL生成步骤]
    E --> F[调用SQL专家智能体]
    F --> G[记录步骤结果]
    G --> H{SQL生成成功?}
    H -->|否| I[记录错误并终止]
    H -->|是| J[安全验证步骤]
    J --> K[调用安全审查智能体]
    K --> L[记录验证结果]
    L --> M{安全验证通过?}
    M -->|否| I
    M -->|是| N[SQL执行步骤]
    N --> O[调用数据库执行智能体]
    O --> P[记录执行结果]
    P --> Q{执行成功?}
    Q -->|否| I
    Q -->|是| R[结果格式化步骤]
    R --> S[调用结果格式化智能体]
    S --> T[记录格式化结果]
    T --> U[计算总执行时间]
    U --> V[生成工作流程摘要]
    V --> W[创建最终响应]
    I --> X[创建错误响应]
    W --> Y[返回给用户]
    X --> Y
```

## 🗄️ 数据库模式获取流程

### 智能表结构分析

```mermaid
graph TD
    A[开始获取数据库模式] --> B[查询所有表名]
    B --> C[遍历每个表]
    C --> D{表是否在缓存中?}
    D -->|是| E[使用缓存数据]
    D -->|否| F[查询表结构信息]
    F --> G[获取列信息]
    G --> H[获取列名]
    H --> I[获取数据类型]
    I --> J[获取是否可空]
    J --> K[获取默认值]
    K --> L[获取列注释]
    L --> M[更新缓存]
    M --> E
    E --> N[格式化表结构]
    N --> O{是否还有表?}
    O -->|是| C
    O -->|否| P[组装完整模式信息]
    P --> Q[返回模式数据]
```

## 🔍 智能表选择算法

### AI模型如何选择相关表

```mermaid
graph TD
    A[分析用户查询] --> B[提取业务关键词]
    B --> C[匹配表名]
    C --> D[匹配列名]
    D --> E[分析业务实体]
    E --> F[推断表关系]
    F --> G[检查外键关系]
    G --> H[识别命名约定]
    H --> I[语义关系分析]
    I --> J[生成表选择方案]
    J --> K[验证关联逻辑]
    K --> L[优化JOIN条件]
    L --> M[生成最终SQL]
```

## 📊 性能监控流程

### 系统性能追踪

```mermaid
graph TD
    A[请求开始] --> B[记录开始时间]
    B --> C[执行各个步骤]
    C --> D[记录每步耗时]
    D --> E[监控资源使用]
    E --> F[追踪数据库连接]
    F --> G[记录查询执行时间]
    G --> H[统计结果处理时间]
    H --> I[计算总响应时间]
    I --> J[生成性能报告]
    J --> K[更新性能指标]
    K --> L[记录到日志系统]
```

这些流程图详细展示了AI智能体文本转SQL系统的各个组件如何协作，以及数据在系统中的流转过程。每个智能体都有明确的职责分工，通过标准化的消息传递机制实现高效协作。
