"""
AI智能体文本转SQL系统 - Web API服务器
基于FastAPI的RESTful API接口
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn

# 导入系统模块
from main import TextToSQLSystem
from utils.logger import get_logger

# 设置日志
logger = get_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="AI智能体文本转SQL系统",
    description="基于AutoGen 0.6框架的多智能体协作系统Web API",
    version="1.0.0"
)

# 全局系统实例
text_to_sql_system = TextToSQLSystem()
query_history: List[Dict[str, Any]] = []
connected_websockets: List[WebSocket] = []

# 请求模型
class QueryRequest(BaseModel):
    query: str
    session_id: Optional[str] = None

class BatchQueryRequest(BaseModel):
    queries: List[str]
    session_id: Optional[str] = None

# 响应模型
class QueryResponse(BaseModel):
    success: bool
    query: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    query_id: str
    timestamp: datetime

class SystemStatusResponse(BaseModel):
    system_initialized: bool
    database_connected: bool
    workflow_status: Dict[str, Any]
    settings: Dict[str, Any]
    error: Optional[str] = None

# 静态文件和模板
app.mount("/static", StaticFiles(directory="frontend/static"), name="static")
templates = Jinja2Templates(directory="frontend/templates")

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """主页面"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/api/initialize")
async def initialize_system():
    """初始化系统"""
    try:
        logger.info("API: 初始化系统请求")
        success = await text_to_sql_system.initialize()
        
        if success:
            # 通知所有WebSocket客户端
            await broadcast_status_update()
            return {"success": True, "message": "系统初始化成功"}
        else:
            raise HTTPException(status_code=500, detail="系统初始化失败")
            
    except Exception as e:
        logger.error(f"API: 系统初始化异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    """处理单个查询"""
    try:
        logger.info(f"API: 处理查询请求: {request.query[:50]}...")
        
        # 检查系统是否已初始化
        if not text_to_sql_system.is_initialized:
            raise HTTPException(status_code=400, detail="系统未初始化，请先初始化系统")
        
        # 生成查询ID
        query_id = str(uuid.uuid4())
        session_id = request.session_id or "web_default"
        
        # 处理查询
        result = await text_to_sql_system.process_query(request.query, session_id)
        
        # 创建响应
        response = QueryResponse(
            success=result.get("success", False),
            query=request.query,
            result=result if result.get("success", False) else None,
            error=str(result.get("error", "")) if not result.get("success", False) else None,
            query_id=query_id,
            timestamp=datetime.now()
        )
        
        # 添加到历史记录
        history_entry = {
            "query_id": query_id,
            "query": request.query,
            "session_id": session_id,
            "result": result,
            "timestamp": datetime.now().isoformat(),
            "success": result.get("success", False)
        }
        query_history.append(history_entry)
        
        # 保持历史记录在合理范围内
        if len(query_history) > 100:
            query_history.pop(0)
        
        # 通知WebSocket客户端
        await broadcast_query_update(history_entry)
        
        return response
        
    except Exception as e:
        logger.error(f"API: 查询处理异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/batch-query")
async def process_batch_query(request: BatchQueryRequest):
    """处理批量查询"""
    try:
        logger.info(f"API: 处理批量查询请求，共 {len(request.queries)} 个查询")
        
        # 检查系统是否已初始化
        if not text_to_sql_system.is_initialized:
            raise HTTPException(status_code=400, detail="系统未初始化，请先初始化系统")
        
        session_id = request.session_id or "web_batch"
        
        # 处理批量查询
        results = await text_to_sql_system.process_batch_queries(request.queries, session_id)
        
        # 生成响应
        responses = []
        for i, (query, result) in enumerate(zip(request.queries, results)):
            query_id = str(uuid.uuid4())
            
            response = {
                "query_id": query_id,
                "query": query,
                "success": result.get("success", False),
                "result": result if result.get("success", False) else None,
                "error": str(result.get("error", "")) if not result.get("success", False) else None,
                "timestamp": datetime.now().isoformat()
            }
            responses.append(response)
            
            # 添加到历史记录
            history_entry = {
                "query_id": query_id,
                "query": query,
                "session_id": session_id,
                "result": result,
                "timestamp": datetime.now().isoformat(),
                "success": result.get("success", False),
                "batch_id": f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }
            query_history.append(history_entry)
        
        # 保持历史记录在合理范围内
        while len(query_history) > 100:
            query_history.pop(0)
        
        return {
            "success": True,
            "total_queries": len(request.queries),
            "results": responses
        }
        
    except Exception as e:
        logger.error(f"API: 批量查询处理异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/status", response_model=SystemStatusResponse)
async def get_system_status():
    """获取系统状态"""
    try:
        logger.info("API: 获取系统状态请求")
        status = await text_to_sql_system.get_system_status()
        
        return SystemStatusResponse(
            system_initialized=status.get("system_initialized", False),
            database_connected=status.get("database_connected", False),
            workflow_status=status.get("workflow_status", {}),
            settings=status.get("settings", {}),
            error=status.get("error")
        )
        
    except Exception as e:
        logger.error(f"API: 获取系统状态异常: {e}")
        return SystemStatusResponse(
            system_initialized=False,
            database_connected=False,
            workflow_status={},
            settings={},
            error=str(e)
        )

@app.get("/api/history")
async def get_query_history(limit: int = 50):
    """获取查询历史"""
    try:
        logger.info(f"API: 获取查询历史请求，限制 {limit} 条")
        
        # 返回最近的查询历史
        recent_history = query_history[-limit:] if len(query_history) > limit else query_history
        
        return {
            "success": True,
            "total_count": len(query_history),
            "returned_count": len(recent_history),
            "history": recent_history
        }
        
    except Exception as e:
        logger.error(f"API: 获取查询历史异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket连接管理
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    await websocket.accept()
    connected_websockets.append(websocket)
    logger.info("WebSocket: 新客户端连接")
    
    try:
        # 发送当前系统状态
        status = await text_to_sql_system.get_system_status()
        await websocket.send_text(json.dumps({
            "type": "status_update",
            "data": status
        }))
        
        # 保持连接
        while True:
            await websocket.receive_text()
            
    except WebSocketDisconnect:
        logger.info("WebSocket: 客户端断开连接")
        connected_websockets.remove(websocket)
    except Exception as e:
        logger.error(f"WebSocket: 连接异常: {e}")
        if websocket in connected_websockets:
            connected_websockets.remove(websocket)

async def broadcast_status_update():
    """广播系统状态更新"""
    if not connected_websockets:
        return
    
    try:
        status = await text_to_sql_system.get_system_status()
        message = json.dumps({
            "type": "status_update",
            "data": status
        })
        
        # 发送给所有连接的客户端
        disconnected = []
        for websocket in connected_websockets:
            try:
                await websocket.send_text(message)
            except:
                disconnected.append(websocket)
        
        # 清理断开的连接
        for ws in disconnected:
            connected_websockets.remove(ws)
            
    except Exception as e:
        logger.error(f"广播状态更新失败: {e}")

async def broadcast_query_update(query_data: Dict[str, Any]):
    """广播查询更新"""
    if not connected_websockets:
        return
    
    try:
        message = json.dumps({
            "type": "query_update",
            "data": query_data
        })
        
        # 发送给所有连接的客户端
        disconnected = []
        for websocket in connected_websockets:
            try:
                await websocket.send_text(message)
            except:
                disconnected.append(websocket)
        
        # 清理断开的连接
        for ws in disconnected:
            connected_websockets.remove(ws)
            
    except Exception as e:
        logger.error(f"广播查询更新失败: {e}")

if __name__ == "__main__":
    logger.info("启动AI智能体文本转SQL系统Web服务器...")
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
