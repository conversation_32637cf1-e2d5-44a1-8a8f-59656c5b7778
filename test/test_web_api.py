"""
Web API测试脚本
测试AI智能体文本转SQL系统的Web API接口
"""

import asyncio
import aiohttp
import json
import pytest
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

class WebAPITester:
    """Web API测试类"""
    
    def __init__(self):
        self.session = None
        self.base_url = API_BASE
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_system_status(self) -> Dict[str, Any]:
        """测试系统状态接口"""
        print("🔍 测试系统状态接口...")
        
        async with self.session.get(f"{self.base_url}/status") as response:
            assert response.status == 200
            data = await response.json()
            
            print(f"   系统初始化状态: {data.get('system_initialized', False)}")
            print(f"   数据库连接状态: {data.get('database_connected', False)}")
            print(f"   AI模型: {data.get('settings', {}).get('openai_model', 'N/A')}")
            
            return data
    
    async def test_system_initialize(self) -> bool:
        """测试系统初始化接口"""
        print("🚀 测试系统初始化接口...")
        
        async with self.session.post(f"{self.base_url}/initialize") as response:
            if response.status == 200:
                data = await response.json()
                success = data.get('success', False)
                print(f"   初始化结果: {'成功' if success else '失败'}")
                return success
            else:
                error_data = await response.json()
                print(f"   初始化失败: {error_data.get('detail', '未知错误')}")
                return False
    
    async def test_single_query(self, query: str) -> Dict[str, Any]:
        """测试单个查询接口"""
        print(f"📝 测试单个查询: {query}")
        
        payload = {
            "query": query,
            "session_id": "test_session"
        }
        
        async with self.session.post(
            f"{self.base_url}/query",
            json=payload
        ) as response:
            data = await response.json()
            
            if response.status == 200:
                success = data.get('success', False)
                print(f"   查询结果: {'成功' if success else '失败'}")
                if success:
                    result = data.get('result', {})
                    if 'summary_text' in result:
                        print(f"   摘要: {result['summary_text'][:100]}...")
                else:
                    print(f"   错误信息: {data.get('error', '未知错误')}")
            else:
                print(f"   请求失败: {data.get('detail', '未知错误')}")
            
            return data
    
    async def test_batch_query(self, queries: list) -> Dict[str, Any]:
        """测试批量查询接口"""
        print(f"📋 测试批量查询，共 {len(queries)} 个查询")
        
        payload = {
            "queries": queries,
            "session_id": "test_batch_session"
        }
        
        async with self.session.post(
            f"{self.base_url}/batch-query",
            json=payload
        ) as response:
            data = await response.json()
            
            if response.status == 200:
                total = data.get('total_queries', 0)
                results = data.get('results', [])
                success_count = sum(1 for r in results if r.get('success', False))
                
                print(f"   总查询数: {total}")
                print(f"   成功数量: {success_count}")
                print(f"   失败数量: {total - success_count}")
            else:
                print(f"   批量查询失败: {data.get('detail', '未知错误')}")
            
            return data
    
    async def test_query_history(self) -> Dict[str, Any]:
        """测试查询历史接口"""
        print("📚 测试查询历史接口...")
        
        async with self.session.get(f"{self.base_url}/history") as response:
            assert response.status == 200
            data = await response.json()
            
            total_count = data.get('total_count', 0)
            returned_count = data.get('returned_count', 0)
            
            print(f"   历史记录总数: {total_count}")
            print(f"   返回记录数: {returned_count}")
            
            return data

async def run_api_tests():
    """运行API测试"""
    print("🧪 开始Web API测试")
    print("=" * 50)
    
    async with WebAPITester() as tester:
        try:
            # 1. 测试系统状态
            status = await tester.test_system_status()
            
            # 2. 如果系统未初始化，先初始化
            if not status.get('system_initialized', False):
                print("\n系统未初始化，正在初始化...")
                await tester.test_system_initialize()
                
                # 再次检查状态
                await asyncio.sleep(2)
                status = await tester.test_system_status()
            
            print("\n" + "=" * 50)
            
            # 3. 测试单个查询
            test_queries = [
                "查询所有用户信息",
                "统计用户总数",
                "查询最近的订单"
            ]
            
            for query in test_queries:
                await tester.test_single_query(query)
                await asyncio.sleep(1)  # 避免请求过快
            
            print("\n" + "=" * 50)
            
            # 4. 测试批量查询
            batch_queries = [
                "统计每个部门的员工数量",
                "计算平均工资",
                "查询最高销售额"
            ]
            
            await tester.test_batch_query(batch_queries)
            
            print("\n" + "=" * 50)
            
            # 5. 测试查询历史
            await tester.test_query_history()
            
            print("\n✅ 所有API测试完成")
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {e}")
            raise

def test_web_api_endpoints():
    """pytest测试函数"""
    asyncio.run(run_api_tests())

if __name__ == "__main__":
    print("🌐 AI智能体文本转SQL系统 - Web API测试")
    print("请确保Web服务器已启动 (python3 start_web_server.py)")
    print("服务器地址: http://localhost:8000")
    print()
    
    try:
        asyncio.run(run_api_tests())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试失败: {e}")
        exit(1)
