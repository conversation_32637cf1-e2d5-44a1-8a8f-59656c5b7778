/* AI智能体文本转SQL系统 - 样式文件 */

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
    color: #4a5568;
    font-size: 2rem;
    margin-bottom: 5px;
}

.header-content p {
    color: #718096;
    font-size: 1rem;
}

.system-status {
    display: flex;
    gap: 20px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #4a5568;
}

.status-indicator {
    font-size: 12px;
}

.status-indicator.online {
    color: #48bb78;
}

.status-indicator.offline {
    color: #f56565;
}

.status-indicator.warning {
    color: #ed8936;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    gap: 20px;
    flex: 1;
}

/* 侧边栏 */
.sidebar {
    width: 250px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    /* height: fit-content; */
}

.nav-menu {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    background: transparent;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    color: #4a5568;
    text-align: left;
}

.nav-item:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.nav-item.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-item i {
    width: 20px;
    text-align: center;
}

/* 内容区域 */
.content-area {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.panel h2 {
    color: #4a5568;
    margin-bottom: 25px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 表单样式 */
.query-form, .batch-form {
    margin-bottom: 30px;
}

.input-group {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

textarea {
    flex: 1;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s ease;
}

textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-danger:hover {
    background: #e53e3e;
}

/* 查询示例 */
.query-examples {
    margin-bottom: 30px;
}

.query-examples h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
}

.example-btn {
    padding: 12px 16px;
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 8px;
    color: #667eea;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.example-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    border-color: #667eea;
}

/* 结果区域 */
.result-area, .batch-result {
    min-height: 300px;
    border: 2px dashed #e2e8f0;
    border-radius: 10px;
    padding: 20px;
    background: #f7fafc;
}

.result-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 260px;
    color: #a0aec0;
    text-align: center;
}

.result-placeholder i {
    font-size: 3rem;
    margin-bottom: 15px;
}

.result-placeholder p {
    font-size: 1.1rem;
}

/* 查询结果样式 */
.query-result {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.query-result.success {
    border-left: 4px solid #48bb78;
}

.query-result.error {
    border-left: 4px solid #f56565;
}

.result-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e8f0;
}

.result-query {
    font-weight: 500;
    color: #4a5568;
    flex: 1;
}

.result-time {
    font-size: 0.9rem;
    color: #718096;
}

.result-content {
    color: #2d3748;
    line-height: 1.6;
}

.result-sql {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    margin: 10px 0;
    overflow-x: auto;
}

.result-data {
    margin-top: 15px;
}

.result-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.result-table th,
.result-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.result-table th {
    background: #f7fafc;
    font-weight: 600;
    color: #4a5568;
}

/* 历史记录 */
.history-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.history-list {
    max-height: 600px;
    overflow-y: auto;
}

.history-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.history-item.success {
    border-left: 4px solid #48bb78;
}

.history-item.error {
    border-left: 4px solid #f56565;
}

/* 系统状态 */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-card h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.status-details {
    color: #718096;
    line-height: 1.6;
}

.status-details .status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.system-actions {
    display: flex;
    gap: 15px;
}

/* 加载和通知 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 15px;
}

.loading {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
}

.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.notification {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
    animation: slideIn 0.3s ease;
    max-width: 400px;
}

.notification.success {
    border-left-color: #48bb78;
}

.notification.error {
    border-left-color: #f56565;
}

.notification.warning {
    border-left-color: #ed8936;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .nav-menu {
        flex-direction: row;
        overflow-x: auto;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .examples-grid {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .system-actions {
        flex-direction: column;
    }
}
