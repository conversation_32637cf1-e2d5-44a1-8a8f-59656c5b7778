// AI智能体文本转SQL系统 - 前端JavaScript

class TextToSQLApp {
    constructor() {
        this.websocket = null;
        this.currentTab = 'query';
        this.queryHistory = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupWebSocket();
        this.loadSystemStatus();
        this.loadQueryHistory();
    }

    setupEventListeners() {
        // 导航菜单
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const tab = e.currentTarget.dataset.tab;
                this.switchTab(tab);
            });
        });

        // 查询表单
        document.getElementById('submitQuery').addEventListener('click', () => {
            this.submitQuery();
        });

        document.getElementById('queryInput').addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.submitQuery();
            }
        });

        // 查询示例
        document.querySelectorAll('.example-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const query = e.target.dataset.query;
                document.getElementById('queryInput').value = query;
            });
        });

        // 批量查询
        document.getElementById('submitBatch').addEventListener('click', () => {
            this.submitBatchQuery();
        });

        // 历史记录
        document.getElementById('refreshHistory').addEventListener('click', () => {
            this.loadQueryHistory();
        });

        document.getElementById('clearHistory').addEventListener('click', () => {
            this.clearHistory();
        });

        // 系统状态
        document.getElementById('initializeSystem').addEventListener('click', () => {
            this.initializeSystem();
        });

        document.getElementById('refreshStatus').addEventListener('click', () => {
            this.loadSystemStatus();
        });
    }

    setupWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            console.log('WebSocket连接已建立');
            this.showNotification('WebSocket连接成功', 'success');
        };
        
        this.websocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };
        
        this.websocket.onclose = () => {
            console.log('WebSocket连接已关闭');
            this.showNotification('WebSocket连接断开', 'warning');
            // 尝试重连
            setTimeout(() => {
                this.setupWebSocket();
            }, 5000);
        };
        
        this.websocket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.showNotification('WebSocket连接错误', 'error');
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'status_update':
                this.updateSystemStatus(data.data);
                break;
            case 'query_update':
                this.addToHistory(data.data);
                break;
        }
    }

    switchTab(tabName) {
        // 更新导航菜单
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');

        this.currentTab = tabName;

        // 加载对应数据
        if (tabName === 'history') {
            this.loadQueryHistory();
        } else if (tabName === 'status') {
            this.loadSystemStatus();
        }
    }

    async submitQuery() {
        const queryInput = document.getElementById('queryInput');
        const query = queryInput.value.trim();
        
        if (!query) {
            this.showNotification('请输入查询内容', 'warning');
            return;
        }

        this.showLoading(true);
        
        try {
            const response = await fetch('/api/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: query,
                    session_id: 'web_' + Date.now()
                })
            });

            const result = await response.json();
            
            if (response.ok) {
                this.displayQueryResult(result);
                queryInput.value = ''; // 清空输入框
                this.showNotification('查询执行成功', 'success');
            } else {
                throw new Error(result.detail || '查询失败');
            }
        } catch (error) {
            console.error('查询错误:', error);
            this.showNotification(`查询失败: ${error.message}`, 'error');
            this.displayQueryError(query, error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async submitBatchQuery() {
        const batchInput = document.getElementById('batchInput');
        const queries = batchInput.value.trim().split('\n').filter(q => q.trim());
        
        if (queries.length === 0) {
            this.showNotification('请输入批量查询内容', 'warning');
            return;
        }

        this.showLoading(true);
        
        try {
            const response = await fetch('/api/batch-query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    queries: queries,
                    session_id: 'web_batch_' + Date.now()
                })
            });

            const result = await response.json();
            
            if (response.ok) {
                this.displayBatchResult(result);
                batchInput.value = ''; // 清空输入框
                this.showNotification(`批量查询完成，共处理 ${result.total_queries} 个查询`, 'success');
            } else {
                throw new Error(result.detail || '批量查询失败');
            }
        } catch (error) {
            console.error('批量查询错误:', error);
            this.showNotification(`批量查询失败: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    displayQueryResult(result) {
        const resultArea = document.getElementById('queryResult');
        
        const resultHtml = `
            <div class="query-result ${result.success ? 'success' : 'error'}">
                <div class="result-header">
                    <div class="result-query">${result.query}</div>
                    <div class="result-time">${new Date(result.timestamp).toLocaleString()}</div>
                </div>
                <div class="result-content">
                    ${this.formatQueryResult(result)}
                </div>
            </div>
        `;
        
        resultArea.innerHTML = resultHtml;
    }

    displayQueryError(query, error) {
        const resultArea = document.getElementById('queryResult');
        
        const resultHtml = `
            <div class="query-result error">
                <div class="result-header">
                    <div class="result-query">${query}</div>
                    <div class="result-time">${new Date().toLocaleString()}</div>
                </div>
                <div class="result-content">
                    <p style="color: #f56565;"><i class="fas fa-exclamation-triangle"></i> 查询失败</p>
                    <p>${error}</p>
                </div>
            </div>
        `;
        
        resultArea.innerHTML = resultHtml;
    }

    displayBatchResult(result) {
        const batchResult = document.getElementById('batchResult');
        
        let resultHtml = `
            <div class="batch-summary">
                <h3>批量查询结果</h3>
                <p>总查询数: ${result.total_queries}</p>
                <p>成功: ${result.results.filter(r => r.success).length}</p>
                <p>失败: ${result.results.filter(r => !r.success).length}</p>
            </div>
        `;
        
        result.results.forEach((queryResult, index) => {
            resultHtml += `
                <div class="query-result ${queryResult.success ? 'success' : 'error'}">
                    <div class="result-header">
                        <div class="result-query">查询 ${index + 1}: ${queryResult.query}</div>
                        <div class="result-time">${new Date(queryResult.timestamp).toLocaleString()}</div>
                    </div>
                    <div class="result-content">
                        ${this.formatQueryResult(queryResult)}
                    </div>
                </div>
            `;
        });
        
        batchResult.innerHTML = resultHtml;
    }

    formatQueryResult(result) {
        if (!result.success) {
            return `<p style="color: #f56565;"><i class="fas fa-exclamation-triangle"></i> ${result.error}</p>`;
        }

        const data = result.result;
        let html = '';

        // 显示摘要
        if (data.summary_text) {
            html += `<div class="result-summary">${data.summary_text}</div>`;
        }

        // 显示格式化输出
        if (data.final_result && data.final_result.formatted_output) {
            html += `<div class="result-formatted">${data.final_result.formatted_output}</div>`;
        }

        // 显示SQL语句
        if (data.sql_result && data.sql_result.sql) {
            html += `
                <div class="result-sql">
                    <strong>生成的SQL:</strong><br>
                    <code>${data.sql_result.sql}</code>
                </div>
            `;
        }

        // 显示数据表格
        if (data.execution_result && data.execution_result.data && data.execution_result.data.length > 0) {
            html += this.formatDataTable(data.execution_result.data);
        }

        return html || '<p>查询执行完成</p>';
    }

    formatDataTable(data) {
        if (!data || data.length === 0) return '';

        const headers = Object.keys(data[0]);
        let tableHtml = `
            <div class="result-data">
                <strong>查询结果:</strong>
                <table class="result-table">
                    <thead>
                        <tr>
                            ${headers.map(header => `<th>${header}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.forEach(row => {
            tableHtml += '<tr>';
            headers.forEach(header => {
                tableHtml += `<td>${row[header] || ''}</td>`;
            });
            tableHtml += '</tr>';
        });

        tableHtml += `
                    </tbody>
                </table>
            </div>
        `;

        return tableHtml;
    }

    async loadQueryHistory() {
        try {
            const response = await fetch('/api/history');
            const result = await response.json();
            
            if (result.success) {
                this.queryHistory = result.history;
                this.displayHistory();
            } else {
                throw new Error('获取历史记录失败');
            }
        } catch (error) {
            console.error('加载历史记录错误:', error);
            this.showNotification('加载历史记录失败', 'error');
        }
    }

    displayHistory() {
        const historyList = document.getElementById('historyList');

        if (this.queryHistory.length === 0) {
            historyList.innerHTML = `
                <div class="result-placeholder">
                    <i class="fas fa-clock"></i>
                    <p>暂无查询历史</p>
                </div>
            `;
            return;
        }

        let historyHtml = '';
        // 创建历史记录的副本并反转，避免修改原数组
        const reversedHistory = [...this.queryHistory].reverse();
        reversedHistory.forEach(item => {
            historyHtml += `
                <div class="history-item ${item.success ? 'success' : 'error'}" onclick="app.showHistoryDetail('${item.query_id}')">
                    <div class="result-header">
                        <div class="result-query">${this.escapeHtml(item.query)}</div>
                        <div class="result-time">${new Date(item.timestamp).toLocaleString()}</div>
                    </div>
                    <div class="history-status">
                        <i class="fas fa-${item.success ? 'check-circle' : 'exclamation-circle'}"></i>
                        ${item.success ? '成功' : '失败'}
                    </div>
                </div>
            `;
        });

        historyList.innerHTML = historyHtml;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    addToHistory(historyItem) {
        this.queryHistory.push(historyItem);
        if (this.currentTab === 'history') {
            this.displayHistory();
        }
    }

    showHistoryDetail(queryId) {
        const item = this.queryHistory.find(h => h.query_id === queryId);
        if (item) {
            // 切换到查询标签页并显示结果
            this.switchTab('query');
            document.getElementById('queryInput').value = item.query;
            
            if (item.result) {
                this.displayQueryResult({
                    success: item.success,
                    query: item.query,
                    result: item.result,
                    timestamp: item.timestamp,
                    query_id: item.query_id
                });
            }
        }
    }

    clearHistory() {
        if (confirm('确定要清空查询历史吗？')) {
            this.queryHistory = [];
            this.displayHistory();
            this.showNotification('历史记录已清空', 'success');
        }
    }

    async loadSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            this.updateSystemStatus(status);
        } catch (error) {
            console.error('加载系统状态错误:', error);
            this.showNotification('加载系统状态失败', 'error');
        }
    }

    updateSystemStatus(status) {
        // 更新头部状态指示器
        const systemIndicator = document.getElementById('systemIndicator');
        const dbIndicator = document.getElementById('dbIndicator');
        
        systemIndicator.className = `fas fa-circle status-indicator ${status.system_initialized ? 'online' : 'offline'}`;
        dbIndicator.className = `fas fa-database status-indicator ${status.database_connected ? 'online' : 'offline'}`;

        // 更新状态面板
        if (this.currentTab === 'status') {
            this.displaySystemStatus(status);
        }
    }

    displaySystemStatus(status) {
        // 系统信息
        document.getElementById('systemInfo').innerHTML = `
            <div class="status-item">
                <span>系统状态:</span>
                <span style="color: ${status.system_initialized ? '#48bb78' : '#f56565'}">
                    ${status.system_initialized ? '已初始化' : '未初始化'}
                </span>
            </div>
        `;

        // 数据库信息
        document.getElementById('databaseInfo').innerHTML = `
            <div class="status-item">
                <span>连接状态:</span>
                <span style="color: ${status.database_connected ? '#48bb78' : '#f56565'}">
                    ${status.database_connected ? '已连接' : '未连接'}
                </span>
            </div>
            <div class="status-item">
                <span>数据库名:</span>
                <span>${status.settings?.database || 'N/A'}</span>
            </div>
        `;

        // AI模型信息
        document.getElementById('modelInfo').innerHTML = `
            <div class="status-item">
                <span>模型:</span>
                <span>${status.settings?.openai_model || 'N/A'}</span>
            </div>
            <div class="status-item">
                <span>安全检查:</span>
                <span>${status.settings?.security_enabled ? '已启用' : '已禁用'}</span>
            </div>
        `;

        // 工作流程状态
        const workflowStatus = status.workflow_status || {};
        document.getElementById('workflowInfo').innerHTML = `
            <div class="status-item">
                <span>协调者状态:</span>
                <span>${workflowStatus.coordinator_status || 'unknown'}</span>
            </div>
            <div class="status-item">
                <span>智能体数量:</span>
                <span>${workflowStatus.agent_count || 0}</span>
            </div>
        `;
    }

    async initializeSystem() {
        this.showLoading(true);
        
        try {
            const response = await fetch('/api/initialize', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showNotification('系统初始化成功', 'success');
                this.loadSystemStatus();
            } else {
                throw new Error(result.detail || '系统初始化失败');
            }
        } catch (error) {
            console.error('系统初始化错误:', error);
            this.showNotification(`系统初始化失败: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (show) {
            overlay.classList.add('show');
        } else {
            overlay.classList.remove('show');
        }
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('notificationContainer');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;
        
        container.appendChild(notification);
        
        // 自动移除通知
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// 初始化应用
const app = new TextToSQLApp();
