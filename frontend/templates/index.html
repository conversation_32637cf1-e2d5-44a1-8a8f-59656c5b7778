<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能体文本转SQL系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-robot"></i> AI智能体文本转SQL系统</h1>
                <p>基于 AutoGen 0.6 框架的多智能体协作系统</p>
            </div>
            <div class="system-status" id="systemStatus">
                <div class="status-item">
                    <i class="fas fa-circle status-indicator" id="systemIndicator"></i>
                    <span>系统状态</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-database status-indicator" id="dbIndicator"></i>
                    <span>数据库</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧面板 -->
            <aside class="sidebar">
                <nav class="nav-menu">
                    <button class="nav-item active" data-tab="query">
                        <i class="fas fa-search"></i>
                        <span>智能查询</span>
                    </button>
                    <button class="nav-item" data-tab="batch">
                        <i class="fas fa-list"></i>
                        <span>批量查询</span>
                    </button>
                    <button class="nav-item" data-tab="history">
                        <i class="fas fa-history"></i>
                        <span>查询历史</span>
                    </button>
                    <button class="nav-item" data-tab="status">
                        <i class="fas fa-cog"></i>
                        <span>系统状态</span>
                    </button>
                </nav>
            </aside>

            <!-- 右侧内容区域 -->
            <section class="content-area">
                <!-- 智能查询面板 -->
                <div class="tab-content active" id="queryTab">
                    <div class="panel">
                        <h2><i class="fas fa-search"></i> 智能查询</h2>
                        <div class="query-form">
                            <div class="input-group">
                                <textarea 
                                    id="queryInput" 
                                    placeholder="请输入您的自然语言查询，例如：查询所有用户信息、统计每个部门的员工数量..."
                                    rows="3"
                                ></textarea>
                                <button id="submitQuery" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                    执行查询
                                </button>
                            </div>
                        </div>
                        
                        <div class="query-examples">
                            <h3>查询示例：</h3>
                            <div class="examples-grid">
                                <button class="example-btn" data-query="查询所有用户信息">
                                    查询所有用户信息
                                </button>
                                <button class="example-btn" data-query="统计每个部门的员工数量">
                                    统计每个部门的员工数量
                                </button>
                                <button class="example-btn" data-query="查找工资大于5000的员工">
                                    查找工资大于5000的员工
                                </button>
                                <button class="example-btn" data-query="按年龄排序显示前10名员工">
                                    按年龄排序显示前10名员工
                                </button>
                            </div>
                        </div>

                        <div class="result-area" id="queryResult">
                            <div class="result-placeholder">
                                <i class="fas fa-lightbulb"></i>
                                <p>输入您的查询，AI智能体将为您生成并执行SQL语句</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量查询面板 -->
                <div class="tab-content" id="batchTab">
                    <div class="panel">
                        <h2><i class="fas fa-list"></i> 批量查询</h2>
                        <div class="batch-form">
                            <div class="input-group">
                                <textarea 
                                    id="batchInput" 
                                    placeholder="请输入多个查询，每行一个查询：&#10;查询所有用户信息&#10;统计每个部门的员工数量&#10;计算平均工资"
                                    rows="6"
                                ></textarea>
                                <button id="submitBatch" class="btn btn-primary">
                                    <i class="fas fa-play"></i>
                                    批量执行
                                </button>
                            </div>
                        </div>
                        
                        <div class="batch-result" id="batchResult">
                            <div class="result-placeholder">
                                <i class="fas fa-tasks"></i>
                                <p>输入多个查询进行批量处理</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 查询历史面板 -->
                <div class="tab-content" id="historyTab">
                    <div class="panel">
                        <h2><i class="fas fa-history"></i> 查询历史</h2>
                        <div class="history-controls">
                            <button id="refreshHistory" class="btn btn-secondary">
                                <i class="fas fa-refresh"></i>
                                刷新
                            </button>
                            <button id="clearHistory" class="btn btn-danger">
                                <i class="fas fa-trash"></i>
                                清空历史
                            </button>
                        </div>
                        
                        <div class="history-list" id="historyList">
                            <div class="result-placeholder">
                                <i class="fas fa-clock"></i>
                                <p>暂无查询历史</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统状态面板 -->
                <div class="tab-content" id="statusTab">
                    <div class="panel">
                        <h2><i class="fas fa-cog"></i> 系统状态</h2>
                        <div class="status-grid" id="statusGrid">
                            <div class="status-card">
                                <h3>系统信息</h3>
                                <div class="status-details" id="systemInfo">
                                    <div class="loading">加载中...</div>
                                </div>
                            </div>
                            
                            <div class="status-card">
                                <h3>数据库连接</h3>
                                <div class="status-details" id="databaseInfo">
                                    <div class="loading">加载中...</div>
                                </div>
                            </div>
                            
                            <div class="status-card">
                                <h3>AI模型配置</h3>
                                <div class="status-details" id="modelInfo">
                                    <div class="loading">加载中...</div>
                                </div>
                            </div>
                            
                            <div class="status-card">
                                <h3>工作流程状态</h3>
                                <div class="status-details" id="workflowInfo">
                                    <div class="loading">加载中...</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="system-actions">
                            <button id="initializeSystem" class="btn btn-primary">
                                <i class="fas fa-power-off"></i>
                                初始化系统
                            </button>
                            <button id="refreshStatus" class="btn btn-secondary">
                                <i class="fas fa-refresh"></i>
                                刷新状态
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>处理中...</p>
        </div>
    </div>

    <!-- 通知容器 -->
    <div class="notification-container" id="notificationContainer"></div>

    <script src="/static/js/app.js"></script>
</body>
</html>
