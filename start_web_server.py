#!/usr/bin/env python3
"""
AI智能体文本转SQL系统 - Web服务器启动脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from utils.logger import setup_logger, get_logger
from config.settings import settings

# 设置日志
setup_logger()
logger = get_logger(__name__)

def check_environment():
    """检查运行环境"""
    logger.info("检查运行环境...")
    
    # 检查必要的目录
    required_dirs = [
        "frontend/static/css",
        "frontend/static/js", 
        "frontend/templates"
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            logger.error(f"缺少必要目录: {dir_path}")
            return False
    
    # 检查必要的文件
    required_files = [
        "frontend/templates/index.html",
        "frontend/static/css/style.css",
        "frontend/static/js/app.js",
        "api_server.py"
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            logger.error(f"缺少必要文件: {file_path}")
            return False
    
    logger.info("环境检查通过")
    return True

def print_startup_info():
    """打印启动信息"""
    print("=" * 60)
    print("🤖 AI智能体文本转SQL系统 - Web界面")
    print("=" * 60)
    print(f"📊 数据库: {settings.database.database}")
    print(f"🤖 AI模型: {settings.openai.model}")
    print(f"🔒 安全检查: {'启用' if settings.security.enable_sql_injection_check else '禁用'}")
    print("=" * 60)
    print("🌐 Web服务器启动中...")
    print("📱 请在浏览器中访问: http://localhost:8000")
    print("🔧 API文档地址: http://localhost:8000/docs")
    print("=" * 60)
    print("💡 使用说明:")
    print("   • 在智能查询页面输入自然语言查询")
    print("   • 支持批量查询处理")
    print("   • 可查看查询历史和系统状态")
    print("   • 实时WebSocket连接显示系统状态")
    print("=" * 60)
    print("⚠️  注意事项:")
    print("   • 首次使用请先点击'初始化系统'")
    print("   • 确保数据库连接配置正确")
    print("   • 查看系统状态确认所有组件正常")
    print("=" * 60)

async def main():
    """主函数"""
    try:
        # 检查环境
        if not check_environment():
            print("❌ 环境检查失败，请检查文件完整性")
            return
        
        # 打印启动信息
        print_startup_info()
        
        # 导入并启动服务器
        import uvicorn
        from api_server import app
        
        # 启动Web服务器
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info",
            access_log=True
        )
        
        server = uvicorn.Server(config)
        await server.serve()
        
    except KeyboardInterrupt:
        print("\n👋 Web服务器已停止")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序异常退出: {e}")
